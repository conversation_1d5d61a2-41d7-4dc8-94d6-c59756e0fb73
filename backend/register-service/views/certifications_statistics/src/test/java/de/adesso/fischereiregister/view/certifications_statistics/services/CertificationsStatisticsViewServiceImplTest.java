package de.adesso.fischereiregister.view.certifications_statistics.services;

import de.adesso.fischereiregister.view.certifications_statistics.persistence.CertificationsStatisticsView;
import de.adesso.fischereiregister.view.certifications_statistics.persistence.CertificationsStatisticsViewRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CertificationsStatisticsViewServiceImplTest {

    @Mock
    private CertificationsStatisticsViewRepository repository;

    private CertificationsStatisticsViewServiceImpl service;

    @BeforeEach
    void setUp() {
        service = new CertificationsStatisticsViewServiceImpl(repository);
    }

    @Test
    @DisplayName("Should create new statistic when none exists")
    void shouldCreateNewStatisticWhenNoneExists() {
        // given
        String federalState = "SH";
        String office = "TestOffice";
        int year = 2024;

        when(repository.findByFederalStateAndIssuerAndYear(federalState, office, year))
                .thenReturn(Optional.empty());

        // when
        service.updateOrCreateStatistic(federalState, office, year);

        // then
        ArgumentCaptor<CertificationsStatisticsView> viewCaptor = ArgumentCaptor.forClass(CertificationsStatisticsView.class);
        verify(repository).save(viewCaptor.capture());

        CertificationsStatisticsView savedView = viewCaptor.getValue();
        assertThat(savedView.getFederalState()).isEqualTo(federalState);
        assertThat(savedView.getIssuer()).isEqualTo(office);
        assertThat(savedView.getYear()).isEqualTo(year);
        assertThat(savedView.getCount()).isEqualTo(1);
    }

    @Test
    @DisplayName("Should update existing statistic when one exists")
    void shouldUpdateExistingStatisticWhenOneExists() {
        // given
        String federalState = "SH";
        String office = "TestOffice";
        int year = 2024;

        CertificationsStatisticsView existingView = new CertificationsStatisticsView();
        existingView.setFederalState(federalState);
        existingView.setIssuer(office);
        existingView.setYear(year);
        existingView.setCount(5);

        when(repository.findByFederalStateAndIssuerAndYear(federalState, office, year))
                .thenReturn(Optional.of(existingView));

        // when
        service.updateOrCreateStatistic(federalState, office, year);

        // then
        ArgumentCaptor<CertificationsStatisticsView> viewCaptor = ArgumentCaptor.forClass(CertificationsStatisticsView.class);
        verify(repository).save(viewCaptor.capture());

        CertificationsStatisticsView savedView = viewCaptor.getValue();
        assertThat(savedView.getFederalState()).isEqualTo(federalState);
        assertThat(savedView.getIssuer()).isEqualTo(office);
        assertThat(savedView.getYear()).isEqualTo(year);
        assertThat(savedView.getCount()).isEqualTo(6); // Incremented from 5 to 6
    }

    @Test
    @DisplayName("Should get statistics by federal state and years")
    void shouldGetStatisticsByFederalStateAndYears() {
        // given
        String federalState = "SH";
        List<Integer> years = List.of(2023, 2024);
        List<CertificationsStatisticsView> expectedViews = List.of(new CertificationsStatisticsView());

        when(repository.findByFederalStateAndYearIn(federalState, years)).thenReturn(expectedViews);

        // when
        List<CertificationsStatisticsView> result = service.getStatisticsByFederalStateAndYears(federalState, years);

        // then
        assertThat(result).isEqualTo(expectedViews);
        verify(repository).findByFederalStateAndYearIn(federalState, years);
    }

    @Test
    @DisplayName("Should get statistics by issuer and years")
    void shouldGetStatisticsByIssuerAndYears() {
        // given
        String issuer = "TestOffice";
        List<Integer> years = List.of(2023, 2024);
        List<CertificationsStatisticsView> expectedViews = List.of(new CertificationsStatisticsView());

        when(repository.findByIssuerAndYearIn(issuer, years)).thenReturn(expectedViews);

        // when
        List<CertificationsStatisticsView> result = service.getStatisticsByIssuerAndYears(issuer, years);

        // then
        assertThat(result).isEqualTo(expectedViews);
        verify(repository).findByIssuerAndYearIn(issuer, years);
    }

    @Test
    @DisplayName("Should get statistics by years")
    void shouldGetStatisticsByYears() {
        // given
        List<Integer> years = List.of(2023, 2024);
        List<CertificationsStatisticsView> expectedViews = List.of(new CertificationsStatisticsView());

        when(repository.findByYearIn(years)).thenReturn(expectedViews);

        // when
        List<CertificationsStatisticsView> result = service.getStatisticsByYears(years);

        // then
        assertThat(result).isEqualTo(expectedViews);
        verify(repository).findByYearIn(years);
    }

    @Test
    @DisplayName("Should get available years")
    void shouldGetAvailableYears() {
        // given
        List<Integer> expectedYears = List.of(2023, 2024);

        when(repository.findDistinctYears()).thenReturn(expectedYears);

        // when
        List<Integer> result = service.getAvailableYears();

        // then
        assertThat(result).isEqualTo(expectedYears);
        verify(repository).findDistinctYears();
    }

    @Test
    @DisplayName("Should delete all statistics")
    void shouldDeleteAllStatistics() {
        // when
        service.deleteAll();

        // then
        verify(repository).deleteAll();
    }
}
