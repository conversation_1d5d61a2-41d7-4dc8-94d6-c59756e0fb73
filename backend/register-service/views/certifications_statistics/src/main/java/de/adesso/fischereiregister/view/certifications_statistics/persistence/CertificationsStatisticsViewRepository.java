package de.adesso.fischereiregister.view.certifications_statistics.persistence;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

/**
 * Repository for accessing certification statistics views.
 */
public interface CertificationsStatisticsViewRepository extends CrudRepository<CertificationsStatisticsView, Long> {

    @Query("""
            SELECT view FROM CertificationsStatisticsView view
            WHERE view.federalState = :federalState
              AND view.issuer = :issuer
              AND view.year = :year
            """)
    Optional<CertificationsStatisticsView> findByFederalStateAndIssuerAndYear(
            @Param("federalState") String federalState,
            @Param("issuer") String issuer,
            @Param("year") int year
    );

    @Query("SELECT view FROM CertificationsStatisticsView view WHERE view.federalState = :federalState AND view.year IN :years")
    List<CertificationsStatisticsView> findByFederalStateAndYearIn(
            @Param("federalState") String federalState,
            @Param("years") List<Integer> years
    );

    @Query("SELECT view FROM CertificationsStatisticsView view WHERE view.issuer = :issuer AND view.year IN :years")
    List<CertificationsStatisticsView> findByIssuerAndYearIn(
            @Param("issuer") String issuer,
            @Param("years") List<Integer> years
    );

    @Query("SELECT view FROM CertificationsStatisticsView view WHERE view.year IN :years")
    List<CertificationsStatisticsView> findByYearIn(@Param("years") List<Integer> years);

    @Query("SELECT DISTINCT view.year FROM CertificationsStatisticsView view ORDER BY view.year DESC")
    List<Integer> findDistinctYears();
}
