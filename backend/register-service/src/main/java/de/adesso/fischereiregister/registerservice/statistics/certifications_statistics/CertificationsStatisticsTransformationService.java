package de.adesso.fischereiregister.registerservice.statistics.certifications_statistics;

import de.adesso.fischereiregister.view.certifications_statistics.persistence.CertificationsStatisticsView;

import java.util.List;

/**
 * Service for transforming certification statistics view objects into domain model objects.
 */
public interface CertificationsStatisticsTransformationService {
    /**
     * Transforms a list of CertificationsStatisticsView objects into a list of CertificationsStatistics objects.
     * This transformation groups the statistics by year and aggregates the data.
     *
     * @param statisticsViews The list of CertificationsStatisticsView objects to transform.
     * @return A list of CertificationsStatistics objects.
     */
    List<CertificationsStatistics> transformToCertificationsStatistics(List<CertificationsStatisticsView> statisticsViews);
}
