package de.adesso.fischereiregister.registerservice.statistics;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.core.events.QualificationsProofCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.QualificationsProof;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.type.QualificationsProofType;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import de.adesso.fischereiregister.view.certifications_statistics.eventhandling.CertificationsStatisticsViewEventHandler;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.hamcrest.Matchers.greaterThanOrEqualTo;
import static org.hamcrest.Matchers.hasSize;
import static org.mockito.Mockito.mock;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class CertificationsStatisticsReadIntegrationTest {

    // greaterThanOrEqualTo is used for the assertions as the data is still contaminated by the file test-data.xml, please correct when the file gets deleted

    public static final String PARAM_YEAR = "year";
    public static final String PARAM_FEDERAL_STATE = "federalState";
    public static final String PARAM_OFFICE = "office";
    private static final String TEST_FEDERAL_STATE_SH = "SH";
    private static final String TEST_FEDERAL_STATE_HH = "HH";
    private static final String TEST_OFFICE_1 = "TestOffice1";
    private static final String TEST_OFFICE_2 = "TestOffice2";
    private static final int CURRENT_YEAR = LocalDate.now().getYear();
    private static final int PREVIOUS_YEAR = CURRENT_YEAR - 1;

    @Autowired
    private CertificationsStatisticsViewEventHandler eventHandler;

    @Autowired
    private MockMvc mvc;

    @BeforeAll
    void setUp() {
        // Create certifications for current year
        QualificationsProofCreatedEvent qualificationsProofCreatedEventSH1 = createCertificationEvent(TEST_FEDERAL_STATE_SH, TEST_OFFICE_1, CURRENT_YEAR);
        QualificationsProofCreatedEvent qualificationsProofCreatedEventSH2 = createCertificationEvent(TEST_FEDERAL_STATE_SH, TEST_OFFICE_1, CURRENT_YEAR);
        QualificationsProofCreatedEvent qualificationsProofCreatedEventHH = createCertificationEvent(TEST_FEDERAL_STATE_HH, TEST_OFFICE_2, CURRENT_YEAR);

        // Create certifications for previous year
        QualificationsProofCreatedEvent qualificationsProofCreatedEventPreviousYearHH = createCertificationEvent(TEST_FEDERAL_STATE_HH, TEST_OFFICE_2, PREVIOUS_YEAR);

        // Create a digitized license with multiple certifications
        RegularLicenseDigitizedEvent digitizedEvent = createDigitizedLicenseEvent(CURRENT_YEAR);

        // Process events
        eventHandler.on(qualificationsProofCreatedEventSH1);
        eventHandler.on(qualificationsProofCreatedEventSH2);
        eventHandler.on(qualificationsProofCreatedEventHH);
        eventHandler.on(qualificationsProofCreatedEventPreviousYearHH);
        eventHandler.on(digitizedEvent);
    }

    private QualificationsProofCreatedEvent createCertificationEvent(String federalState, String office, int year) {
        // Create a qualification proof with specific year
        QualificationsProof qualificationsProof = new QualificationsProof();
        qualificationsProof.setFederalState(federalState);
        qualificationsProof.setIssuedBy(office);
        qualificationsProof.setPassedOn(LocalDate.of(year, 1, 15)); // Set the passed date in the specified year
        qualificationsProof.setType(QualificationsProofType.CERTIFICATE);
        qualificationsProof.setFishingCertificateId("CERT-" + UUID.randomUUID());

        // Create QualificationsProofCreatedEvent with the created proof
        return new QualificationsProofCreatedEvent(
                UUID.randomUUID(), // registerEntryId
                qualificationsProof,
                new Person() // person
        );
    }

    private RegularLicenseDigitizedEvent createDigitizedLicenseEvent(int year) {
        // Create two qualification proofs for the digitized license
        QualificationsProof proof1 = new QualificationsProof();
        proof1.setFederalState(TEST_FEDERAL_STATE_SH);
        proof1.setIssuedBy(TEST_OFFICE_1);
        proof1.setPassedOn(LocalDate.of(year, 2, 20));
        proof1.setType(QualificationsProofType.CERTIFICATE);
        proof1.setFishingCertificateId("CERT-DIG-1-" + UUID.randomUUID());

        QualificationsProof proof2 = new QualificationsProof();
        proof2.setFederalState(TEST_FEDERAL_STATE_HH);
        proof2.setIssuedBy(TEST_OFFICE_2);
        proof2.setPassedOn(LocalDate.of(year, 3, 10));
        proof2.setType(QualificationsProofType.LEGACY_LICENSE);
        proof2.setFishingCertificateId("CERT-DIG-2-" + UUID.randomUUID());

        // Create RegularLicenseDigitizedEvent with the proofs
        return new RegularLicenseDigitizedEvent(
                UUID.randomUUID(), // registerId
                "salt",
                new Person(), // person
                new Jurisdiction(), // jurisdiction
                null, // fishingLicense
                new ArrayList<>(), // fees
                new ArrayList<>(), // taxes
                List.of(proof1, proof2), // qualificationsProofs
                new ArrayList<>(), // identificationDocuments
                mock(JurisdictionConsentInfo.class), // consentInfo
                "Test Office", // issuedByOffice
                "Test Address" // issuedByAddress
        );
    }

    @Test
    @DisplayName("""
            GET /api/statistics/certifications
            Verify that the certifications statistics endpoint can be reached and delivers the proper information.
            """)
    void callGetCertificationsStatisticsSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/certifications")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_SH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Verify year matches parameter
                .andExpect(jsonPath("$[0].data").exists()) // Verify data object exists
                .andExpect(jsonPath("$[0].data[0].issuer").exists()) // Verify issuer exists
                .andExpect(jsonPath("$[0].data[0].amount").value(greaterThanOrEqualTo(3))); // 3 certifications for SH in current year (2 from direct events + 1 from digitized)
    }

    @Test
    @DisplayName("""
            GET /api/statistics/certifications with federal state parameter
            Verify that the certifications statistics endpoint correctly filters by federal state.
            """)
    void callGetCertificationsStatisticsWithFederalStateFilter() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/certifications")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_HH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Verify year matches parameter
                .andExpect(jsonPath("$[0].data[0].issuer").exists()) // Verify issuer exists
                .andExpect(jsonPath("$[0].data[0].amount").value(greaterThanOrEqualTo(2))); // 2 certifications for HH in current year (1 from direct event + 1 from digitized)
    }

    @Test
    @DisplayName("""
            GET /api/statistics/certifications with office parameter
            Verify that the certifications statistics endpoint correctly filters by office.
            """)
    void callGetCertificationsStatisticsWithOfficeFilter() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/certifications")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_OFFICE, TEST_OFFICE_1)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Verify year matches parameter
                .andExpect(jsonPath("$[0].data[0].issuer").exists()) // Verify issuer exists
                .andExpect(jsonPath("$[0].data[0].amount").value(greaterThanOrEqualTo(3))); // 3 certifications for Office1 in current year (2 from direct events + 1 from digitized)
    }

    @Test
    @DisplayName("""
            GET /api/statistics/certifications with year parameter
            Verify that the certifications statistics endpoint correctly filters by year.
            """)
    void callGetCertificationsStatisticsWithYearFilter() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/certifications")
                        .param(PARAM_YEAR, String.valueOf(PREVIOUS_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_HH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(PREVIOUS_YEAR)) // Verify year matches parameter
                .andExpect(jsonPath("$[0].data[0].issuer").exists()) // Verify issuer exists
                .andExpect(jsonPath("$[0].data[0].amount").value(greaterThanOrEqualTo(1))); // 1 certification for HH in previous year
    }

    @Test
    @DisplayName("""
            GET /api/statistics/certifications with multiple year parameters
            Verify that the certifications statistics endpoint correctly handles multiple year parameters.
            """)
    void callGetCertificationsStatisticsWithMultipleYears() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/certifications")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_YEAR, String.valueOf(PREVIOUS_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_HH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$", hasSize(2))) // Years of data (2)
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // First result year (current year)
                .andExpect(jsonPath("$[1].year").value(PREVIOUS_YEAR)) // Second result year (previous year)
                .andExpect(jsonPath("$[0].data[0].amount").exists()) // Amount for current year
                .andExpect(jsonPath("$[1].data[0].amount").exists()); // Count for previous year
    }

    @Test
    @DisplayName("""
            GET /api/statistics/certifications without federalState parameter
            Verify that the certifications statistics endpoint returns data for all federal states when no federalState parameter is provided.
            """)
    void callGetCertificationsStatisticsWithoutFederalStateSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/certifications")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                // Data from all federal states for the current year
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data[0].issuer").value(TEST_OFFICE_1))
                .andExpect(jsonPath("$[0].data[0].amount").value(greaterThanOrEqualTo(3))) // 3 certifications for Office1 in current year (2 from direct events + 1 from digitized)
                .andExpect(jsonPath("$[0].data[1].issuer").value(TEST_OFFICE_2))
                .andExpect(jsonPath("$[0].data[1].amount").value(greaterThanOrEqualTo(2))); // 2 certifications for Office2 in current year (1 from direct event + 1 from digitized)
    }

    @Test
    @DisplayName("""
            GET /api/statistics/certifications without parameters
            Verify that the certifications statistics endpoint returns all available data when no parameters are provided.
            """)
    void callGetCertificationsStatisticsWithoutParametersSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/certifications")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                // Data from all years and federal states
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // First result year (current year)
                .andExpect(jsonPath("$[1].year").value(PREVIOUS_YEAR)) // Second result year (previous year)
                .andExpect(jsonPath("$[*].data").exists()) // Data objects exist
                .andExpect(jsonPath("$[0].data[0].issuer").value(TEST_OFFICE_1))
                .andExpect(jsonPath("$[0].data[0].amount").value(greaterThanOrEqualTo(3))) // 3 certifications for Office1 in current year (2 from direct events + 1 from digitized)
                .andExpect(jsonPath("$[0].data[1].issuer").value(TEST_OFFICE_2))
                .andExpect(jsonPath("$[0].data[1].amount").value(greaterThanOrEqualTo(2))) // 2 certifications for Office2 in current year (1 from direct event + 1 from digitized)
                .andExpect(jsonPath("$[1].data[0].issuer").value(TEST_OFFICE_2))
                .andExpect(jsonPath("$[1].data[0].amount").value(greaterThanOrEqualTo(1))); // 1 certification for Office2 in previous year (1 from direct event)
    }

    @Test
    @DisplayName("""
            GET /api/statistics/certifications with non-existent year parameter
            Verify that the certifications statistics endpoint returns an empty array for a non-existent year.
            """)
    void callGetCertificationsStatisticsWithNonExistentYear() throws Exception {
        String nonExistentYear = String.valueOf(2000); // Use a year with no data

        mvc.perform(MockMvcRequestBuilders.get("/statistics/certifications")
                        .param(PARAM_YEAR, nonExistentYear)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray()) // Response is an array
                .andExpect(jsonPath("$").isEmpty()); // Empty array for non-existent year
    }

    @Test
    @DisplayName("""
            GET /api/statistics/certifications with both office and federal state parameters
            Verify that the office parameter takes precedence over federal state parameter.
            """)
    void callGetCertificationsStatisticsWithBothOfficeAndFederalState() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/certifications")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_OFFICE, TEST_OFFICE_1)
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_HH) // This should be ignored since office is provided
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR))
                .andExpect(jsonPath("$[0].data[0].issuer").exists())
                .andExpect(jsonPath("$[0].data[0].amount").value(greaterThanOrEqualTo(3))); // Should match TEST_OFFICE_1 count (3), not TEST_FEDERAL_STATE_HH count (2)
    }
}
