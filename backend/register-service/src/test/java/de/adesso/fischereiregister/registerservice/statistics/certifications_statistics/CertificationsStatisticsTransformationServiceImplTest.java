package de.adesso.fischereiregister.registerservice.statistics.certifications_statistics;

import de.adesso.fischereiregister.view.certifications_statistics.persistence.CertificationsStatisticsView;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class CertificationsStatisticsTransformationServiceImplTest {

    private CertificationsStatisticsTransformationServiceImpl service;

    @BeforeEach
    void setUp() {
        service = new CertificationsStatisticsTransformationServiceImpl();
    }

    @Test
    @DisplayName("Should transform certification statistics views to domain model")
    void shouldTransformCertificationStatisticsViewsToDomainModel() {
        // given
        CertificationsStatisticsView view1 = createView("SH", "Office1", 2023, 5);
        CertificationsStatisticsView view2 = createView("SH", "Office2", 2023, 3);
        CertificationsStatisticsView view3 = createView("SH", "Office1", 2023, 2);
        CertificationsStatisticsView view4 = createView("SH", "Office1", 2024, 7);
        CertificationsStatisticsView view5 = createView("SH", "Office2", 2024, 4);

        List<CertificationsStatisticsView> views = List.of(view1, view2, view3, view4, view5);

        // when
        List<CertificationsStatistics> result = service.transformToCertificationsStatistics(views);

        // then
        assertThat(result).hasSize(2); // Two years: 2023 and 2024

        // Check 2023 statistics
        CertificationsStatistics stats2023 = result.stream()
                .filter(s -> s.year() == 2023)
                .findFirst()
                .orElseThrow();

        assertThat(stats2023.year()).isEqualTo(2023);
        assertThat(stats2023.data()).hasSize(2); // Two issuers: Office1 and Office2

        // Check Office1 entries for 2023 (should be sum of view1 and view3)
        assertThat(stats2023.data().stream()
                .filter(d -> d.issuer().equals("Office1"))
                .findFirst()
                .orElseThrow()
                .amount()).isEqualTo(7); // 5 + 2

        // Check Office2 entries for 2023 (should be view2)
        assertThat(stats2023.data().stream()
                .filter(d -> d.issuer().equals("Office2"))
                .findFirst()
                .orElseThrow()
                .amount()).isEqualTo(3);

        // Check 2024 statistics
        CertificationsStatistics stats2024 = result.stream()
                .filter(s -> s.year() == 2024)
                .findFirst()
                .orElseThrow();

        assertThat(stats2024.year()).isEqualTo(2024);
        assertThat(stats2024.data()).hasSize(2); // Two issuers: Office1 and Office2

        // Check Office1 entries for 2024 (should be view4)
        assertThat(stats2024.data().stream()
                .filter(d -> d.issuer().equals("Office1"))
                .findFirst()
                .orElseThrow()
                .amount()).isEqualTo(7);

        // Check Office2 entries for 2024 (should be view5)
        assertThat(stats2024.data().stream()
                .filter(d -> d.issuer().equals("Office2"))
                .findFirst()
                .orElseThrow()
                .amount()).isEqualTo(4);
    }

    @Test
    @DisplayName("Should handle empty list of views")
    void shouldHandleEmptyListOfViews() {
        // given
        List<CertificationsStatisticsView> views = List.of();

        // when
        List<CertificationsStatistics> result = service.transformToCertificationsStatistics(views);

        // then
        assertThat(result).isEmpty();
    }

    private CertificationsStatisticsView createView(String federalState, String issuer, int year, int count) {
        CertificationsStatisticsView view = new CertificationsStatisticsView();
        view.setFederalState(federalState);
        view.setIssuer(issuer);
        view.setYear(year);
        view.setCount(count);
        return view;
    }
}
